import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { spawn } from 'child_process';

async function testMCPServer() {
    console.log('🔄 بدء اختبار خادم MCP...');
    
    // تشغيل خادم MCP
    const serverProcess = spawn('node', ['figma-mcp-server.js'], {
        cwd: process.cwd(),
        stdio: ['pipe', 'pipe', 'pipe']
    });
    
    // إنشاء عميل MCP
    const transport = new StdioClientTransport({
        reader: serverProcess.stdout,
        writer: serverProcess.stdin
    });
    
    const client = new Client({
        name: 'test-client',
        version: '1.0.0'
    }, {
        capabilities: {}
    });
    
    try {
        // الاتصال بالخادم
        await client.connect(transport);
        console.log('✅ تم الاتصال بالخادم بنجاح!');
        
        // الحصول على قائمة الأدوات
        const tools = await client.listTools();
        console.log('🛠️ الأدوات المتاحة:', tools);
        
        // اختبار أداة Figma
        if (tools.tools && tools.tools.length > 0) {
            console.log('🎨 اختبار أداة Figma...');
            const result = await client.callTool('figma', {});
            console.log('📊 النتيجة:', result);
        }
        
    } catch (error) {
        console.error('❌ خطأ:', error.message);
    } finally {
        // إغلاق الاتصال
        await client.close();
        serverProcess.kill();
        console.log('🔚 تم إنهاء الاختبار');
    }
}

// تشغيل الاختبار
testMCPServer().catch(console.error);
