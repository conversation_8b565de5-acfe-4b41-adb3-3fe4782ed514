@echo off
echo 🚀 بدء تشغيل خادم MCP Figma...
echo.
echo 📍 المجلد: %CD%
echo 🔧 التكوين: .env
echo.

REM التحقق من وجود ملف .env
if not exist ".env" (
    echo ❌ خطأ: ملف .env غير موجود
    echo يرجى إنشاء ملف .env مع المتغيرات المطلوبة
    pause
    exit /b 1
)

REM التحقق من وجود node_modules
if not exist "node_modules" (
    echo 📦 تنزيل التبعيات...
    npm install
)

echo ✅ بدء تشغيل الخادم...
echo 💡 للإيقاف: اضغط Ctrl+C
echo.

node figma-mcp-server.js

pause
