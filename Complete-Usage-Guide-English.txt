================================================================================
                    Complete Usage Guide - Figma MCP with Augment Code
================================================================================

📋 Table of Contents:
1. Project Information
2. Setup Instructions
3. Usage Methods
4. Available Commands
5. Practical Scenarios
6. Troubleshooting
7. Important Files
8. Recommended Workflow
9. Best Practices
10. Additional Information

================================================================================
1. Project Information
================================================================================

🎯 Purpose: Connect Figma designs with Augment Code to accelerate project development

📁 Project Path: C:/Users/<USER>/mcp-figma-augmentcode-integration

🔑 Required Environment Variables:
- FIGMA_TOKEN: *********************************************
- FIGMA_FILE_ID: accounting
- MCP_PORT: 3000

✅ Status: Successfully downloaded and configured
✅ Security: All security vulnerabilities resolved
✅ Server: Running on port 3000

================================================================================
2. Setup Instructions in Augment Code
================================================================================

🚀 Quick Setup:
1. Open Augment Code
2. Go to: Workspace Settings → MCP
3. Add a new MCP server:
   - Name: figma
   - Command: node "C:/Users/<USER>/mcp-figma-augmentcode-integration/figma-mcp-server.js"
4. Restart Augment Code

🔧 Advanced Setup:
Use the content from augment-code-config.json to get two servers:
- figma: for basic functions
- figma-project: for advanced project management

🧪 Verify Installation:
In Augment Code, type: "Use figma tool to fetch accounting file data"

================================================================================
3. Usage Methods
================================================================================

💻 Server Commands:
- npm start (basic server)
- npm run start:mcp (same as basic)
- npm run start:project (advanced server)
- npm run test:figma (connection test)

🎮 Direct Usage:
- Double-click on start-figma-mcp.bat
- Or use commands in terminal

🤖 Usage with Augment Code:
- Use natural language commands in English
- Request design analysis and code generation
- Extract components and colors automatically

================================================================================
4. Available Commands
================================================================================

🎨 Basic Design Commands:
✅ "Use figma tool to fetch accounting file data"
✅ "Ask figma to get the latest version from design file"
✅ "Use figma to show all components in the design file"
✅ "Ask figma for details about the component named 'ButtonPrimary'"
✅ "Show raw JSON data from figma"

💼 Project Management Commands:
✅ "Use figma to analyze UI components and create development task list"
✅ "Ask figma to extract color system and create CSS variables file"
✅ "Use figma to identify missing components in code compared to design"
✅ "Ask figma to analyze design and create project folder structure"
✅ "Use figma to extract texts and create translation file"

🔧 Advanced Development Commands:
✅ "Use figma to analyze design and create React components"
✅ "Ask figma to extract icons and convert them to SVG components"
✅ "Use figma to analyze layout and create CSS Grid/Flexbox"
✅ "Ask figma to identify responsive breakpoints from design"
✅ "Use figma to create Storybook stories for components"

📊 Analysis and Documentation Commands:
✅ "Use figma to analyze design and create component report"
✅ "Ask figma to compare design versions and identify changes"
✅ "Use figma to create Style Guide from design"
✅ "Ask figma to analyze accessibility in the design"
✅ "Use figma to create User Journey map from screens"

================================================================================
5. Practical Scenarios
================================================================================

🎯 Scenario 1: Starting a New Project
Goal: Create project from Figma design

Steps:
1. "Use figma to analyze design and create project folder structure"
2. "Ask figma to extract color system and create CSS variables file"
3. "Use figma to analyze UI components and create development task list"

🔧 Scenario 2: Component Development
Goal: Convert design to code

Steps:
1. "Use figma to show all components in the design file"
2. "Ask figma for details about the component named 'Button'"
3. "Use figma to analyze design and create React components"
4. "Ask figma to extract icons and convert them to SVG components"

📱 Scenario 3: Responsive Design
Goal: Implement responsive design

Steps:
1. "Ask figma to identify responsive breakpoints from design"
2. "Use figma to analyze layout and create CSS Grid/Flexbox"
3. "Use figma to analyze design and create media queries"

📊 Scenario 4: Project Review and Analysis
Goal: Analyze progress and review quality

Steps:
1. "Use figma to analyze design and create component report"
2. "Ask figma to compare design versions and identify changes"
3. "Use figma to identify missing components in code compared to design"
4. "Ask figma to analyze accessibility in the design"

================================================================================
6. Troubleshooting
================================================================================

❌ Issue: Figma tool doesn't appear in Augment Code
Solution:
1. Ensure MCP server is running
2. Check path correctness in configuration
3. Restart Augment Code

❌ Issue: Connection error with Figma
Solution:
1. Verify FIGMA_TOKEN is correct
2. Ensure FIGMA_FILE_ID is valid
3. Check internet connection

❌ Issue: Slow response
Solution:
1. Use 'summary' format instead of 'json'
2. Request specific components instead of entire file
3. Check Figma server status

❌ Issue: MODULE_NOT_FOUND error
Solution:
1. Ensure npm install was run
2. Check path in configuration
3. Use quotes around the path

================================================================================
7. Important Files
================================================================================

📄 Configuration Files:
- .env (environment variables)
- package.json (project settings)
- claude-desktop-config.json (Claude Desktop configuration)
- augment-code-config.json (Advanced Augment Code configuration)

🔧 Server Files:
- figma-mcp-server.js (basic server)
- project-workflow.js (advanced server)
- test-mcp-client.js (connection test)

📚 Documentation Files:
- README.md (main guide)
- USAGE-GUIDE.md (detailed usage guide)
- prompts.md (ready-to-use commands)
- troubleshooting.md (problem solving)
- augment-mcp-setup.md (setup guide)

🚀 Execution Files:
- start-figma-mcp.bat (quick start for Windows)

================================================================================
8. Recommended Workflow
================================================================================

📅 Phase 1: Analysis and Planning (Day 1)
1. Fetch and analyze design data
2. Create project structure
3. Extract color system and fonts
4. Create task list

📅 Phase 2: Basic Development (Week 1)
1. Create basic components
2. Apply design system
3. Develop main layout
4. Add icons and images

📅 Phase 3: Enhancement and Testing (Week 2)
1. Implement responsive design
2. Test accessibility
3. Optimize performance
4. Quality review

📅 Phase 4: Documentation and Delivery (Day 3)
1. Create usage guide
2. Document components
3. Create Storybook
4. Final delivery

================================================================================
9. Best Practices
================================================================================

✅ Best Practices:
- Always start with complete design analysis
- Use specific and clear commands
- Request component details before development
- Review changes regularly

⚠️ Avoid These Mistakes:
- Don't request all data at once
- Don't ignore accessibility analysis
- Don't forget responsive design review
- Don't neglect documentation

🚀 Speed Tips:
- Use shortcut commands
- Save frequently used commands
- Use appropriate formats (JSON, CSS, etc.)
- Request summaries instead of complete data

================================================================================
10. Additional Information
================================================================================

🔗 Useful Links:
- Model Context Protocol: https://modelcontextprotocol.io
- Figma API: https://www.figma.com/developers/api
- Augment Code: https://www.augmentcode.com/

📞 Support:
If you encounter any issues:
1. Check troubleshooting.md file
2. Verify .env file
3. Test connection using npm run test:figma
4. Review error logs

🎉 Congratulations!
You are now ready to use Figma MCP with Augment Code efficiently!

================================================================================
11. Command Examples for Copy-Paste
================================================================================

🎨 Design Analysis:
"Use figma tool to fetch the complete design data"
"Ask figma to show me all available components"
"Use figma to get details about the Button component"
"Ask figma to extract the color palette from the design"

💻 Development Tasks:
"Use figma to create a development task list with priorities"
"Ask figma to generate React components from the design"
"Use figma to extract CSS variables for the color system"
"Ask figma to create responsive breakpoints"

📊 Project Management:
"Use figma to analyze the project structure and suggest folder organization"
"Ask figma to compare this design with the previous version"
"Use figma to identify missing components in our codebase"
"Ask figma to create a style guide documentation"

🔍 Quality Assurance:
"Use figma to check accessibility compliance in the design"
"Ask figma to analyze the design consistency"
"Use figma to identify potential usability issues"
"Ask figma to suggest performance optimizations"

================================================================================
12. Advanced Features
================================================================================

🛠️ Advanced Server Features (project-workflow.js):
- figma-data: Enhanced data fetching with multiple formats
- project-structure: Automatic folder structure creation
- extract-colors: Color system extraction in multiple formats
- development-tasks: Automated task list generation

📋 Available Formats:
- JSON: Raw data format
- Summary: Human-readable overview
- Components: Component list only
- CSS: CSS variables format
- SCSS: SCSS variables format

🎯 Priority Levels:
- High: Critical components and features
- Medium: Important but not urgent
- Low: Nice-to-have features
- All: Complete task list

================================================================================
13. Integration Examples
================================================================================

🔗 With React Projects:
"Use figma to analyze the design and create React component structure"
"Ask figma to generate TypeScript interfaces from design components"
"Use figma to create styled-components from the design system"

🔗 With CSS Frameworks:
"Use figma to extract design tokens for Tailwind CSS"
"Ask figma to create Bootstrap custom variables"
"Use figma to generate CSS Grid layouts"

🔗 With Design Systems:
"Use figma to create a complete design system documentation"
"Ask figma to generate Storybook stories for all components"
"Use figma to create design tokens in JSON format"

================================================================================
File created automatically on: 2025-06-19
Last updated: On file save
================================================================================
