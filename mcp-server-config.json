{"name": "figma", "description": "MCP server for Figma integration with Augment Code", "version": "1.0.0", "server": {"command": "node", "args": ["figma-mcp-server.js"], "cwd": "C:/Users/<USER>/mcp-figma-augmentcode-integration", "env_file": ".env"}, "capabilities": {"tools": [{"name": "figma", "description": "Fetch Figma file data", "parameters": {"component": {"type": "string", "description": "Optional component name to filter", "required": false}}}]}, "connection": {"type": "stdio", "port": 3000}}