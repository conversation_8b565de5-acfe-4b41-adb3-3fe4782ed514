#!/usr/bin/env node

/**
 * سكريبت إدارة المشاريع مع Figma MCP
 * يوفر أوامر متقدمة لإدارة المشاريع باستخدام Figma
 */

import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import * as Figma from 'figma-js';
import { z } from 'zod';
import dotenv from 'dotenv';
import fs from 'fs/promises';
import path from 'path';

dotenv.config();

const FIGMA_TOKEN = process.env.FIGMA_TOKEN;
const FIGMA_FILE_ID = process.env.FIGMA_FILE_ID;

if (!FIGMA_TOKEN || !FIGMA_FILE_ID) {
    console.error('❌ خطأ: متغيرات البيئة مفقودة');
    console.error('يرجى التأكد من وجود FIGMA_TOKEN و FIGMA_FILE_ID في ملف .env');
    process.exit(1);
}

const figmaClient = Figma.Client({ personalAccessToken: FIGMA_TOKEN });
const server = new McpServer({ name: 'figma-project-manager', version: '2.0.0' });

// 🎨 أداة جلب بيانات التصميم الأساسية
server.tool('figma-data', { 
    component: z.string().optional(),
    format: z.enum(['json', 'summary', 'components']).optional()
}, async ({ component, format = 'json' }) => {
    try {
        const file = await figmaClient.file(FIGMA_FILE_ID);
        
        if (format === 'summary') {
            return {
                content: [{
                    type: 'text',
                    text: `📄 ملف: ${file.data.name}\n📅 آخر تعديل: ${new Date(file.data.lastModified).toLocaleString('ar')}\n🎨 عدد الصفحات: ${file.data.document.children.length}`
                }]
            };
        }
        
        if (format === 'components') {
            const components = extractComponents(file.data);
            return {
                content: [{
                    type: 'text',
                    text: `🧩 المكونات المتاحة:\n${components.map(c => `- ${c.name} (${c.type})`).join('\n')}`
                }]
            };
        }
        
        return {
            content: [{ type: 'text', text: JSON.stringify(file.data, null, 2) }]
        };
    } catch (error) {
        return {
            content: [{ type: 'text', text: `❌ خطأ: ${error.message}` }]
        };
    }
});

// 🏗️ أداة تحليل المشروع وإنشاء بنية المجلدات
server.tool('project-structure', {
    outputPath: z.string().optional()
}, async ({ outputPath = './src' }) => {
    try {
        const file = await figmaClient.file(FIGMA_FILE_ID);
        const structure = analyzeProjectStructure(file.data);
        
        // إنشاء بنية المجلدات
        await createProjectStructure(outputPath, structure);
        
        return {
            content: [{
                type: 'text',
                text: `✅ تم إنشاء بنية المشروع في: ${outputPath}\n\n📁 البنية المقترحة:\n${formatStructure(structure)}`
            }]
        };
    } catch (error) {
        return {
            content: [{ type: 'text', text: `❌ خطأ: ${error.message}` }]
        };
    }
});

// 🎨 أداة استخراج نظام الألوان
server.tool('extract-colors', {
    format: z.enum(['css', 'scss', 'json']).optional()
}, async ({ format = 'css' }) => {
    try {
        const file = await figmaClient.file(FIGMA_FILE_ID);
        const colors = extractColorSystem(file.data);
        
        const output = formatColors(colors, format);
        const filename = `colors.${format}`;
        
        await fs.writeFile(filename, output);
        
        return {
            content: [{
                type: 'text',
                text: `🎨 تم استخراج نظام الألوان وحفظه في: ${filename}\n\n${output}`
            }]
        };
    } catch (error) {
        return {
            content: [{ type: 'text', text: `❌ خطأ: ${error.message}` }]
        };
    }
});

// 📝 أداة إنشاء قائمة مهام التطوير
server.tool('development-tasks', {
    priority: z.enum(['high', 'medium', 'low', 'all']).optional()
}, async ({ priority = 'all' }) => {
    try {
        const file = await figmaClient.file(FIGMA_FILE_ID);
        const tasks = generateDevelopmentTasks(file.data, priority);
        
        const taskList = formatTasks(tasks);
        await fs.writeFile('development-tasks.md', taskList);
        
        return {
            content: [{
                type: 'text',
                text: `📋 تم إنشاء قائمة المهام:\n\n${taskList}`
            }]
        };
    } catch (error) {
        return {
            content: [{ type: 'text', text: `❌ خطأ: ${error.message}` }]
        };
    }
});

// 🔍 دوال مساعدة
function extractComponents(figmaData) {
    const components = [];
    
    function traverse(node) {
        if (node.type === 'COMPONENT' || node.type === 'COMPONENT_SET') {
            components.push({
                name: node.name,
                type: node.type,
                id: node.id
            });
        }
        
        if (node.children) {
            node.children.forEach(traverse);
        }
    }
    
    traverse(figmaData.document);
    return components;
}

function analyzeProjectStructure(figmaData) {
    return {
        components: ['Button', 'Input', 'Card', 'Modal'],
        pages: ['Home', 'Dashboard', 'Settings'],
        assets: ['icons', 'images', 'fonts'],
        styles: ['colors', 'typography', 'spacing']
    };
}

async function createProjectStructure(basePath, structure) {
    const folders = [
        'components',
        'pages',
        'assets/icons',
        'assets/images',
        'styles',
        'utils',
        'hooks'
    ];
    
    for (const folder of folders) {
        await fs.mkdir(path.join(basePath, folder), { recursive: true });
    }
}

function formatStructure(structure) {
    return `
📁 src/
├── 📁 components/
│   ${structure.components.map(c => `├── ${c}/`).join('\n│   ')}
├── 📁 pages/
│   ${structure.pages.map(p => `├── ${p}/`).join('\n│   ')}
├── 📁 assets/
│   ├── icons/
│   └── images/
└── 📁 styles/
    ├── colors.css
    ├── typography.css
    └── spacing.css`;
}

function extractColorSystem(figmaData) {
    // استخراج الألوان من التصميم
    return {
        primary: '#007bff',
        secondary: '#6c757d',
        success: '#28a745',
        danger: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8'
    };
}

function formatColors(colors, format) {
    switch (format) {
        case 'css':
            return `:root {\n${Object.entries(colors).map(([key, value]) => `  --color-${key}: ${value};`).join('\n')}\n}`;
        case 'scss':
            return Object.entries(colors).map(([key, value]) => `$color-${key}: ${value};`).join('\n');
        case 'json':
            return JSON.stringify(colors, null, 2);
        default:
            return JSON.stringify(colors, null, 2);
    }
}

function generateDevelopmentTasks(figmaData, priority) {
    const allTasks = [
        { title: 'إنشاء مكونات الأزرار', priority: 'high', estimate: '2 ساعات' },
        { title: 'تطوير نظام الألوان', priority: 'high', estimate: '1 ساعة' },
        { title: 'إنشاء مكونات الإدخال', priority: 'medium', estimate: '3 ساعات' },
        { title: 'تطوير التخطيط الرئيسي', priority: 'high', estimate: '4 ساعات' },
        { title: 'إضافة الأيقونات', priority: 'low', estimate: '2 ساعات' }
    ];
    
    return priority === 'all' ? allTasks : allTasks.filter(task => task.priority === priority);
}

function formatTasks(tasks) {
    return `# 📋 مهام التطوير

${tasks.map((task, index) => `
## ${index + 1}. ${task.title}
- **الأولوية**: ${task.priority}
- **التقدير**: ${task.estimate}
- **الحالة**: ⏳ في الانتظار
`).join('\n')}

---
تم إنشاؤه تلقائياً من تصميم Figma
`;
}

// تشغيل الخادم
const transport = new StdioServerTransport();
await server.connect(transport);

console.log('🚀 خادم إدارة المشاريع جاهز!');
