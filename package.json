{"name": "mcp-figma-augmentcode-integration", "version": "1.0.0", "description": "MCP server for Figma integration with Augment Code", "type": "module", "main": "figma-mcp-server.js", "scripts": {"start": "node figma-mcp-server.js", "start:mcp": "node figma-mcp-server.js", "start:project": "node project-workflow.js", "dev": "node figma-mcp-server.js", "test:figma": "node test-mcp-client.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "dotenv": "^16.3.0", "figma-js": "^1.16.1-0", "zod": "^3.22.0"}, "keywords": ["mcp", "figma", "augment-code", "design"], "author": "", "license": "MIT"}