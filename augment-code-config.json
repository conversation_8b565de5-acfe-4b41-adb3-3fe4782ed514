{"mcpServers": {"figma": {"command": "node", "args": ["figma-mcp-server.js"], "cwd": "C:/Users/<USER>/mcp-figma-augmentcode-integration", "description": "خادم MCP للتكامل مع Figma - يوفر أدوات لجلب وتحليل تصاميم Figma", "capabilities": ["جلب بيانات التصميم", "تحليل المكونات", "استخراج الألوان", "إنشاء قوائم المهام", "تحليل بنية المشروع"]}, "figma-project": {"command": "node", "args": ["project-workflow.js"], "cwd": "C:/Users/<USER>/mcp-figma-augmentcode-integration", "description": "خادم MCP متقدم لإدارة المشاريع مع Figma - يوفر أدوات متقدمة لإدارة المشاريع", "capabilities": ["إنشاء بنية المشروع", "استخراج نظام الألوان", "إنشاء قوائم مهام مفصلة", "تحليل المكونات المتقدم", "إنشاء ملفات التكوين"]}}, "settings": {"autoRestart": true, "timeout": 30000, "retries": 3, "logLevel": "info"}, "environment": {"NODE_ENV": "production", "MCP_LOG_LEVEL": "info"}}