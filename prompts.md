# أوامر Augment Code للمشاريع مع Figma

## 🎨 أوامر التصميم الأساسية
✅ استخدم أداة figma لجلب بيانات ملف المحاسبة
✅ اطلب من figma إحضار آخر إصدار من ملف التصميم
✅ استخدم figma لعرض جميع المكونات في ملف التصميم
✅ اطلب من figma تفاصيل المكون المسمى "ButtonPrimary"
✅ أظهر البيانات الخام JSON من figma

## 💼 أوامر إدارة المشاريع
✅ استخدم figma لتحليل مكونات واجهة المستخدم وإنشاء قائمة مهام للتطوير
✅ اطلب من figma استخراج ألوان النظام وإنشاء ملف CSS variables
✅ استخدم figma لتحديد المكونات المفقودة في الكود مقارنة بالتصميم
✅ اطلب من figma تحليل التصميم وإنشاء بنية مجلدات للمشروع
✅ استخدم figma لاستخراج النصوص وإنشاء ملف ترجمة

## 🔧 أوامر التطوير المتقدمة
✅ استخدم figma لتحليل التصميم وإنشاء مكونات React
✅ اطلب من figma استخراج الأيقونات وتحويلها إلى SVG components
✅ استخدم figma لتحليل التخطيط وإنشاء CSS Grid/Flexbox
✅ اطلب من figma تحديد responsive breakpoints من التصميم
✅ استخدم figma لإنشاء Storybook stories للمكونات

## 📊 أوامر التحليل والتوثيق
✅ استخدم figma لتحليل التصميم وإنشاء تقرير مكونات
✅ اطلب من figma مقارنة إصدارات التصميم وتحديد التغييرات
✅ استخدم figma لإنشاء دليل أسلوب (Style Guide) من التصميم
✅ اطلب من figma تحليل إمكانية الوصول (Accessibility) في التصميم
✅ استخدم figma لإنشاء خريطة المستخدم (User Journey) من الشاشات
