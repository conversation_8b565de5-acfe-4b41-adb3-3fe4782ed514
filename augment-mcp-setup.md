# إعداد خادم MCP Figma في Augment Code

## الخطوات:

### 1. فتح إعدادات MCP
- افتح Augment Code
- اذهب إلى: **Workspace Settings → MCP**

### 2. إضافة خادم جديد
**Name:** `figma`

**Command:**
```
node "C:/Users/<USER>/mcp-figma-augmentcode-integration/figma-mcp-server.js"
```

### 3. متغيرات البيئة (اختياري)
إذا لم تكن تستخدم ملف .env، أضف:
- `FIGMA_TOKEN`: *********************************************
- `FIGMA_FILE_ID`: accounting
- `MCP_PORT`: 3000

### 4. إعادة تشغيل Augment Code
أعد تشغيل Augment Code لتطبيق التغييرات.

## التحقق من التشغيل:
بعد إعادة التشغيل، يجب أن ترى أداة "figma" متاحة في Augment Code.
