import * as Figma from 'figma-js';
import dotenv from 'dotenv';

dotenv.config();

const FIGMA_TOKEN = process.env.FIGMA_TOKEN;
const FIGMA_FILE_ID = process.env.FIGMA_FILE_ID;

console.log('🔍 اختبار اتصال Figma...');
console.log('Token:', FIGMA_TOKEN ? 'موجود ✅' : 'غير موجود ❌');
console.log('File ID:', FIGMA_FILE_ID || 'غير محدد ❌');

if (!FIGMA_TOKEN) {
    console.error('❌ خطأ: FIGMA_TOKEN غير موجود');
    process.exit(1);
}

if (!FIGMA_FILE_ID) {
    console.error('❌ خطأ: FIGMA_FILE_ID غير موجود');
    process.exit(1);
}

const figmaClient = Figma.Client({ personalAccessToken: FIGMA_TOKEN });

try {
    console.log('🔄 جاري الاتصال بـ Figma...');
    const file = await figmaClient.file(FIGMA_FILE_ID);
    console.log('✅ تم الاتصال بنجاح!');
    console.log('📄 اسم الملف:', file.data.name);
    console.log('📅 آخر تعديل:', new Date(file.data.lastModified).toLocaleString('ar'));
    console.log('🎨 عدد الصفحات:', file.data.document.children.length);
} catch (error) {
    console.error('❌ خطأ في الاتصال:', error.message);
    if (error.response) {
        console.error('📊 حالة الاستجابة:', error.response.status);
        console.error('📝 تفاصيل الخطأ:', error.response.data);
    }
}
